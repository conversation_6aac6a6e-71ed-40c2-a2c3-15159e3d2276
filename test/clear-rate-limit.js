#!/usr/bin/env node

/**
 * Clear Rate Limit Script
 * 
 * Clears rate limiting cache to allow testing
 */

import { redis } from '../utils/redis.js';

async function clearRateLimit() {
  try {
    console.log('🧹 Clearing rate limit cache...');
    
    // Get all rate limit keys
    const keys = await redis.keys('ratelimit:*');
    
    if (keys.length > 0) {
      console.log(`Found ${keys.length} rate limit keys:`, keys);
      
      // Delete all rate limit keys
      await redis.del(...keys);
      console.log('✅ Rate limit cache cleared successfully!');
    } else {
      console.log('ℹ️  No rate limit keys found');
    }
    
    // Also clear any OTP keys for testing
    const otpKeys = await redis.keys('otp:*');
    if (otpKeys.length > 0) {
      console.log(`Found ${otpKeys.length} OTP keys, clearing...`);
      await redis.del(...otpKeys);
      console.log('✅ OTP cache cleared successfully!');
    }
    
    console.log('🎉 Cache clearing completed!');
    process.exit(0);
    
  } catch (error) {
    console.error('❌ Error clearing cache:', error);
    process.exit(1);
  }
}

clearRateLimit();
