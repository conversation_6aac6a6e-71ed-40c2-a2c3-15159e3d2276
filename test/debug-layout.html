<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Layout Debug</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100">
    <div class="min-h-screen bg-gray-50">
        <!-- Sidebar -->
        <div class="fixed inset-y-0 left-0 z-40 w-64 bg-blue-600 text-white flex flex-col">
            <div class="p-4">
                <h1 class="text-lg font-bold">Sidebar</h1>
            </div>
            <nav class="flex-1 p-4">
                <ul class="space-y-2">
                    <li><a href="#" class="block p-2 bg-blue-700 rounded">Dashboard</a></li>
                    <li><a href="#" class="block p-2 hover:bg-blue-700 rounded">Products</a></li>
                    <li><a href="#" class="block p-2 hover:bg-blue-700 rounded">Orders</a></li>
                </ul>
            </nav>
        </div>

        <!-- Main content area -->
        <div class="lg:ml-64 flex flex-col min-h-screen">
            <!-- Main content -->
            <main class="flex-1 p-8 bg-white">
                <h1 class="text-2xl font-bold mb-4">Main Content</h1>
                <p>This content should be beside the sidebar, not behind it.</p>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mt-8">
                    <div class="bg-blue-100 p-4 rounded">Card 1</div>
                    <div class="bg-green-100 p-4 rounded">Card 2</div>
                    <div class="bg-yellow-100 p-4 rounded">Card 3</div>
                </div>
            </main>
        </div>
    </div>
</body>
</html>
