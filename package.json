{"name": "next-js-heroui", "version": "0.0.1", "private": true, "type": "module", "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "eslint . --ext .js,.jsx -c .eslintrc.json --fix"}, "dependencies": {"@heroicons/react": "^2.2.0", "@heroui/button": "2.2.16", "@heroui/card": "^2.2.20", "@heroui/chip": "^2.2.17", "@heroui/code": "2.2.12", "@heroui/divider": "^2.2.15", "@heroui/input": "2.4.16", "@heroui/kbd": "2.2.12", "@heroui/link": "2.2.13", "@heroui/listbox": "2.3.15", "@heroui/navbar": "2.2.14", "@heroui/snippet": "2.2.17", "@heroui/switch": "2.2.14", "@heroui/system": "2.4.12", "@heroui/theme": "2.4.12", "@react-aria/ssr": "3.9.7", "@react-aria/visually-hidden": "3.8.20", "clsx": "2.1.1", "dotenv": "^16.5.0", "framer-motion": "11.13.1", "ioredis": "^5.6.1", "jose": "^6.0.11", "js-cookie": "^3.0.5", "next": "15.0.4", "next-themes": "^0.4.4", "pg": "^8.14.1", "react": "18.3.1", "react-dom": "18.3.1"}, "devDependencies": {"@next/eslint-plugin-next": "15.0.4", "@react-types/shared": "3.25.0", "autoprefixer": "10.4.19", "concurrently": "^9.1.2", "eslint": "^8.57.0", "eslint-config-next": "15.0.4", "eslint-config-prettier": "9.1.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jsx-a11y": "^6.4.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "5.2.1", "eslint-plugin-react": "^7.23.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-unused-imports": "4.1.4", "postcss": "8.4.49", "prettier": "3.3.3", "tailwind-variants": "0.3.0", "tailwindcss": "3.4.16"}}