import { heroui } from "@heroui/theme";

/** @type {import('tailwindcss').Config} */
const config = {
  content: [
    "./components/**/*.{js,ts,jsx,tsx,mdx}",
    "./app/**/*.{js,ts,jsx,tsx,mdx}",
    "./node_modules/@heroui/theme/dist/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      fontFamily: {
        sans: ["var(--font-sans)"],
        mono: ["var(--font-mono)"],
      },
      colors: {
        // Custom dark mode colors for better contrast
        dark: {
          50: '#f8fafc',
          100: '#f1f5f9',
          200: '#e2e8f0',
          300: '#cbd5e1',
          400: '#94a3b8',
          500: '#64748b',
          600: '#475569',
          700: '#334155',
          800: '#1e293b', // Better than pure black
          900: '#0f172a', // Softer dark background
          950: '#020617',
        }
      },
      backgroundColor: {
        // Override default dark backgrounds
        'dark-primary': '#1e293b',
        'dark-secondary': '#334155',
        'dark-surface': '#0f172a',
      }
    },
  },
  darkMode: "class",
  plugins: [heroui()],
};

export default config;
