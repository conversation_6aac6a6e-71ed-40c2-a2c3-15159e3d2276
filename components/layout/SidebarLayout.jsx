"use client";

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import Sidebar from './Sidebar';

/**
 * Sidebar Layout Component
 * 
 * Provides the main layout structure with sidebar and content area.
 * Handles responsive behavior and integrates with authentication.
 */
export default function SidebarLayout({ user, children }) {
  const [loading, setLoading] = useState(false);
  const router = useRouter();

  // Handle logout
  const handleLogout = async () => {
    setLoading(true);
    
    try {
      const response = await fetch('/api/auth/logout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      // Clear client-side state and redirect regardless of API response
      router.refresh(); // This will trigger server-side re-render
    } catch (error) {
      console.error('Logout error:', error);
      // Still redirect on error
      router.refresh();
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-dark-surface">
      {/* Sidebar */}
      <Sidebar 
        user={user} 
        onLogout={handleLogout} 
        isLoading={loading} 
      />

      {/* Main content area */}
      <div className="lg:pl-64 flex flex-col min-h-screen">
        {/* Mobile header spacer */}
        <div className="lg:hidden h-16" />
        
        {/* Main content */}
        <main className="flex-1 px-4 sm:px-6 lg:px-8 py-8">
          {children}
        </main>
      </div>
    </div>
  );
}
