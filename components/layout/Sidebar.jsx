"use client";

import { useState, useEffect } from 'react';
import { Button } from '@heroui/button';
import { Divider } from '@heroui/divider';
import { ThemeSwitch } from '@/components/theme-switch';
import { useSidebar } from './SidebarLayout';
import { 
  HomeIcon, 
  ShoppingBagIcon, 
  ShoppingCartIcon, 
  ChartBarIcon,
  CogIcon,
  UsersIcon,
  Bars3Icon,
  XMarkIcon
} from '@heroicons/react/24/outline';

/**
 * Responsive Sidebar Component
 * 
 * Features:
 * - Responsive design with hamburger menu on mobile
 * - Theme switching integration
 * - Navigation items with icons
 * - Collapsible on desktop
 */
export default function Sidebar({ user, onLogout, isLoading }) {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [mounted, setMounted] = useState(false);
  const { isCollapsed, setIsCollapsed } = useSidebar();

  // Handle hydration
  useEffect(() => {
    setMounted(true);
  }, []);

  const navigationItems = [
    { name: 'Dashboard', href: '/dashboard', icon: HomeIcon, current: true },
    { name: 'Products', href: '/products', icon: ShoppingBagIcon, current: false },
    { name: 'Orders', href: '/orders', icon: ShoppingCartIcon, current: false },
    { name: 'Analytics', href: '/analytics', icon: ChartBarIcon, current: false },
    { name: 'Customers', href: '/customers', icon: UsersIcon, current: false },
    { name: 'Settings', href: '/settings', icon: CogIcon, current: false },
  ];

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const toggleCollapse = () => {
    setIsCollapsed(!isCollapsed);
  };

  const getUserRoleDisplay = () => {
    if (user?.role === 'super_admin') return 'Super Admin';
    if (user?.role === 'admin') return 'Admin';
    return 'User';
  };

  const getRoleColor = () => {
    if (user?.role === 'super_admin') return 'danger';
    if (user?.role === 'admin') return 'warning';
    return 'primary';
  };

  return (
    <>
      {/* Mobile header with hamburger menu */}
      <div className="lg:hidden fixed top-0 left-0 right-0 z-50 bg-white dark:bg-slate-800 border-b border-gray-200 dark:border-slate-700 shadow-sm">
        <div className="flex items-center justify-between h-16 px-4">
          <Button
            isIconOnly
            variant="light"
            onPress={toggleMobileMenu}
            className="text-gray-700 dark:text-gray-300"
          >
            {isMobileMenuOpen ? (
              <XMarkIcon className="h-6 w-6" />
            ) : (
              <Bars3Icon className="h-6 w-6" />
            )}
          </Button>
          <h1 className="text-lg font-semibold text-gray-900 dark:text-white">
            Adimas Admin
          </h1>
          {mounted && <ThemeSwitch />}
        </div>
      </div>

      {/* Mobile menu overlay */}
      {isMobileMenuOpen && (
        <div
          className="lg:hidden fixed inset-0 z-30 bg-black bg-opacity-50 cursor-pointer"
          onClick={toggleMobileMenu}
        />
      )}

      {/* Sidebar */}
      <div className={`
        fixed left-0 z-40 transform transition-transform duration-300 ease-in-out
        ${isMobileMenuOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}
        ${isCollapsed ? 'lg:w-16' : 'lg:w-64'}
        w-64 bg-white dark:bg-slate-800 border-r border-gray-200 dark:border-slate-600
        flex flex-col shadow-xl lg:shadow-none
        top-16 lg:top-0 bottom-0 lg:inset-y-0
      `}>
        {/* Logo/Header - Hidden on mobile since it's in the top bar */}
        <div className="hidden lg:flex items-center justify-between h-16 px-4 border-b border-gray-200 dark:border-slate-600">
          {!isCollapsed && (
            <h1 className="text-lg font-bold text-gray-900 dark:text-white truncate">
              Adimas Admin
            </h1>
          )}
          <Button
            isIconOnly
            variant="light"
            size="sm"
            onPress={toggleCollapse}
            className="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white"
          >
            <Bars3Icon className="h-5 w-5" />
          </Button>
        </div>

        {/* Navigation */}
        <nav className="flex-1 px-3 py-6 space-y-1 overflow-y-auto">
          {navigationItems.map((item) => {
            const Icon = item.icon;
            return (
              <a
                key={item.name}
                href={item.href}
                className={`
                  flex items-center px-3 py-3 text-sm font-medium rounded-xl transition-all duration-200
                  ${item.current
                    ? 'bg-blue-600 text-white shadow-lg shadow-blue-600/25'
                    : 'text-gray-700 dark:text-slate-300 hover:bg-gray-100 dark:hover:bg-slate-700/50 hover:text-gray-900 dark:hover:text-white'
                  }
                  ${isCollapsed ? 'justify-center' : ''}
                  group relative
                `}
                title={isCollapsed ? item.name : ''}
              >
                <Icon className={`h-5 w-5 ${isCollapsed ? '' : 'mr-3'} flex-shrink-0 ${item.current ? 'text-white' : ''}`} />
                {!isCollapsed && <span>{item.name}</span>}
                {isCollapsed && (
                  <div className="absolute left-full ml-2 px-2 py-1 bg-gray-900 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none whitespace-nowrap z-50">
                    {item.name}
                  </div>
                )}
              </a>
            );
          })}
        </nav>

        {/* Theme Switch */}
        <div className="px-3 py-4 border-t border-gray-200 dark:border-slate-600">
          <div className={`flex items-center ${isCollapsed ? 'justify-center' : 'justify-between'}`}>
            {!isCollapsed && (
              <span className="text-sm font-medium text-gray-700 dark:text-slate-300">
                Theme
              </span>
            )}
            {mounted && <ThemeSwitch />}
          </div>
        </div>

        {/* User Info and Logout */}
        <div className="px-3 py-4 border-t border-gray-200 dark:border-slate-600 bg-gray-50 dark:bg-slate-900/50">
          {!isCollapsed && (
            <div className="mb-3 p-3 bg-white dark:bg-slate-800 rounded-lg border border-gray-200 dark:border-slate-600">
              <p className="text-sm font-semibold text-gray-900 dark:text-white truncate">
                {user?.name || user?.email}
              </p>
              <p className="text-xs text-gray-500 dark:text-slate-400 mt-1">
                {getUserRoleDisplay()}
              </p>
            </div>
          )}
          <Button
            color="danger"
            variant={isCollapsed ? "flat" : "ghost"}
            size="sm"
            onPress={onLogout}
            isLoading={isLoading}
            className={`w-full ${isCollapsed ? 'px-2' : ''} font-medium`}
          >
            {isCollapsed ? '⏻' : (isLoading ? 'Logging out...' : 'Logout')}
          </Button>
        </div>
      </div>
    </>
  );
}
