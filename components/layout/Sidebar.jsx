"use client";

import { useState, useEffect } from 'react';
import { Button } from '@heroui/button';
import { Divider } from '@heroui/divider';
import { ThemeSwitch } from '@/components/theme-switch';
import { 
  HomeIcon, 
  ShoppingBagIcon, 
  ShoppingCartIcon, 
  ChartBarIcon,
  CogIcon,
  UsersIcon,
  Bars3Icon,
  XMarkIcon
} from '@heroicons/react/24/outline';

/**
 * Responsive Sidebar Component
 * 
 * Features:
 * - Responsive design with hamburger menu on mobile
 * - Theme switching integration
 * - Navigation items with icons
 * - Collapsible on desktop
 */
export default function Sidebar({ user, onLogout, isLoading }) {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [mounted, setMounted] = useState(false);

  // Handle hydration
  useEffect(() => {
    setMounted(true);
  }, []);

  const navigationItems = [
    { name: 'Dashboard', href: '/dashboard', icon: HomeIcon, current: true },
    { name: 'Products', href: '/products', icon: ShoppingBagIcon, current: false },
    { name: 'Orders', href: '/orders', icon: ShoppingCartIcon, current: false },
    { name: 'Analytics', href: '/analytics', icon: ChartBarIcon, current: false },
    { name: 'Customers', href: '/customers', icon: UsersIcon, current: false },
    { name: 'Settings', href: '/settings', icon: CogIcon, current: false },
  ];

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const toggleCollapse = () => {
    setIsCollapsed(!isCollapsed);
  };

  const getUserRoleDisplay = () => {
    if (user?.role === 'super_admin') return 'Super Admin';
    if (user?.role === 'admin') return 'Admin';
    return 'User';
  };

  const getRoleColor = () => {
    if (user?.role === 'super_admin') return 'danger';
    if (user?.role === 'admin') return 'warning';
    return 'primary';
  };

  return (
    <>
      {/* Mobile menu button */}
      <div className="lg:hidden fixed top-4 left-4 z-50">
        <Button
          isIconOnly
          variant="flat"
          onPress={toggleMobileMenu}
          className="bg-white dark:bg-dark-800 shadow-lg"
        >
          {isMobileMenuOpen ? (
            <XMarkIcon className="h-6 w-6" />
          ) : (
            <Bars3Icon className="h-6 w-6" />
          )}
        </Button>
      </div>

      {/* Mobile menu overlay */}
      {isMobileMenuOpen && (
        <div
          className="lg:hidden fixed inset-0 z-40 bg-black bg-opacity-50 cursor-pointer"
          onClick={toggleMobileMenu}
        />
      )}

      {/* Sidebar */}
      <div className={`
        fixed inset-y-0 left-0 z-40 transform transition-transform duration-300 ease-in-out
        lg:translate-x-0 lg:static lg:inset-0
        ${isMobileMenuOpen ? 'translate-x-0' : '-translate-x-full'}
        ${isCollapsed ? 'lg:w-16' : 'lg:w-64'}
        w-64 bg-white dark:bg-dark-800 border-r border-gray-200 dark:border-dark-700
        flex flex-col shadow-lg lg:shadow-none
      `}>
        {/* Logo/Header */}
        <div className="flex items-center justify-between h-16 px-4 border-b border-gray-200 dark:border-dark-700">
          {!isCollapsed && (
            <h1 className="text-lg font-bold text-gray-900 dark:text-white truncate">
              Adimas Admin
            </h1>
          )}
          <Button
            isIconOnly
            variant="light"
            size="sm"
            onPress={toggleCollapse}
            className="hidden lg:flex"
          >
            <Bars3Icon className="h-5 w-5" />
          </Button>
        </div>

        {/* Navigation */}
        <nav className="flex-1 px-4 py-6 space-y-2 overflow-y-auto">
          {navigationItems.map((item) => {
            const Icon = item.icon;
            return (
              <a
                key={item.name}
                href={item.href}
                className={`
                  flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors
                  ${item.current 
                    ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300' 
                    : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-dark-700'
                  }
                  ${isCollapsed ? 'justify-center' : ''}
                `}
                title={isCollapsed ? item.name : ''}
              >
                <Icon className={`h-5 w-5 ${isCollapsed ? '' : 'mr-3'} flex-shrink-0`} />
                {!isCollapsed && <span>{item.name}</span>}
              </a>
            );
          })}
        </nav>

        <Divider />

        {/* Theme Switch */}
        <div className="px-4 py-4">
          <div className={`flex items-center ${isCollapsed ? 'justify-center' : 'justify-between'}`}>
            {!isCollapsed && (
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Theme
              </span>
            )}
            {mounted && <ThemeSwitch />}
          </div>
        </div>

        <Divider />

        {/* User Info and Logout */}
        <div className="px-4 py-4 border-t border-gray-200 dark:border-dark-700">
          {!isCollapsed && (
            <div className="mb-3">
              <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                {user?.name || user?.email}
              </p>
              <p className="text-xs text-gray-500 dark:text-gray-400">
                {getUserRoleDisplay()}
              </p>
            </div>
          )}
          <Button
            color="danger"
            variant={isCollapsed ? "flat" : "light"}
            size="sm"
            onPress={onLogout}
            isLoading={isLoading}
            className={`w-full ${isCollapsed ? 'px-2' : ''}`}
          >
            {isCollapsed ? '⏻' : (isLoading ? 'Logging out...' : 'Logout')}
          </Button>
        </div>
      </div>
    </>
  );
}
